import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiggyz_app/shared/widgets/widgets.dart';
import 'package:wiggyz_app/screens/edit_payment_method_screen.dart';

class PaymentSettingsScreen extends StatefulWidget {
  const PaymentSettingsScreen({super.key});

  @override
  _PaymentSettingsScreenState createState() => _PaymentSettingsScreenState();
}

class _PaymentSettingsScreenState extends State<PaymentSettingsScreen> {
  final List<PaymentMethod> _savedPaymentMethods = [
    PaymentMethod(
      type: PaymentType.upi,
      name: 'UPI - john.doe@okicici',
      isDefault: true,
      lastUsed: 'Last used on 29 May 2025',
    ),
    PaymentMethod(
      type: PaymentType.creditCard,
      name: 'HDFC Credit Card ••••4582',
      isDefault: false,
      lastUsed: 'Last used on 15 May 2025',
    ),
    PaymentMethod(
      type: PaymentType.bankAccount,
      name: 'SBI Account ••••7890',
      isDefault: false,
      lastUsed: 'Last used on 02 May 2025',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[100],
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        title: Text(
          'Payment Settings',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoCard(isDarkMode),
              const SizedBox(height: 24),
              _buildSavedPaymentMethodsSection(isDarkMode),
              const SizedBox(height: 24),
              _buildAddPaymentMethodSection(isDarkMode),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              isDarkMode
                  ? [const Color(0xFF1E1F26), const Color(0xFF2A2C36)]
                  : [const Color(0xFFE0F2F1), const Color(0xFFA5D6A7)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: isDarkMode ? const Color(0xFFD4AF37) : Colors.green[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Payment Information',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color:
                      isDarkMode ? const Color(0xFFD4AF37) : Colors.green[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Set up and manage your payment methods for quick and secure transactions within the Wiggyz Gaming app.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your payment information is securely stored and encrypted.',
            style: GoogleFonts.poppins(
              fontSize: 13,
              fontStyle: FontStyle.italic,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedPaymentMethodsSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Saved Payment Methods',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        _savedPaymentMethods.isEmpty
            ? _buildEmptyState(isDarkMode)
            : ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _savedPaymentMethods.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                return _buildPaymentMethodCard(
                  _savedPaymentMethods[index],
                  isDarkMode,
                );
              },
            ),
      ],
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1F26) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.credit_card_off,
            size: 48,
            color: isDarkMode ? Colors.grey[600] : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Payment Methods Saved',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a payment method to quickly add funds and make purchases.',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodCard(PaymentMethod method, bool isDarkMode) {
    final IconData iconData;
    final Color iconColor;

    switch (method.type) {
      case PaymentType.upi:
        iconData = Icons.account_balance_wallet;
        iconColor = Colors.purple;
        break;
      case PaymentType.creditCard:
        iconData = Icons.credit_card;
        iconColor = Colors.blue;
        break;
      case PaymentType.debitCard:
        iconData = Icons.credit_card;
        iconColor = Colors.green;
        break;
      case PaymentType.bankAccount:
        iconData = Icons.account_balance;
        iconColor = Colors.amber;
        break;
      case PaymentType.paypal:
        iconData = Icons.paypal;
        iconColor = Colors.indigo;
        break;
      case PaymentType.bitcoin:
        iconData = Icons.currency_bitcoin;
        iconColor = Colors.orange;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF2A2C36) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(iconData, color: iconColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        method.name,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (method.isDefault)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFCC00),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Default',
                          style: GoogleFonts.poppins(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  method.lastUsed,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
            ),
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 18),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  if (!method.isDefault)
                    const PopupMenuItem(
                      value: 'default',
                      child: Row(
                        children: [
                          Icon(Icons.check_circle_outline, size: 18),
                          SizedBox(width: 8),
                          Text('Set as Default'),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete_outline, size: 18, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
            onSelected: (value) {
              // Handle menu item selection
              if (value == 'delete') {
                _showDeleteConfirmationDialog(method);
              } else if (value == 'default') {
                _setAsDefault(method);
              } else if (value == 'edit') {
                _editPaymentMethod(method);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAddPaymentMethodSection(bool isDarkMode) {
    final paymentOptions = [
      PaymentOption(
        type: PaymentType.upi,
        title: 'UPI',
        subtitle: 'Pay directly from your bank account',
        icon: Icons.account_balance_wallet,
        color: Colors.purple,
      ),
      PaymentOption(
        type: PaymentType.creditCard,
        title: 'Credit Card',
        subtitle: 'Add a new credit card',
        icon: Icons.credit_card,
        color: Colors.blue,
      ),
      PaymentOption(
        type: PaymentType.debitCard,
        title: 'Debit Card',
        subtitle: 'Add a new debit card',
        icon: Icons.credit_card,
        color: Colors.green,
      ),
      PaymentOption(
        type: PaymentType.bankAccount,
        title: 'Bank Account',
        subtitle: 'Link your bank account directly',
        icon: Icons.account_balance,
        color: Colors.amber,
      ),
      PaymentOption(
        type: PaymentType.paypal,
        title: 'PayPal',
        subtitle: 'Link your PayPal account',
        icon: Icons.paypal,
        color: Colors.indigo,
      ),
      PaymentOption(
        type: PaymentType.bitcoin,
        title: 'Bitcoin',
        subtitle: 'Add your Bitcoin wallet address',
        icon: Icons.currency_bitcoin,
        color: Colors.orange,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Add Payment Method',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: paymentOptions.length,
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          itemBuilder: (context, index) {
            return _buildAddPaymentOptionCard(
              paymentOptions[index],
              isDarkMode,
            );
          },
        ),
      ],
    );
  }

  Widget _buildAddPaymentOptionCard(PaymentOption option, bool isDarkMode) {
    return InkWell(
      onTap: () => _addNewPaymentMethod(option.type),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF2A2C36) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: option.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(option.icon, color: option.color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.title,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    option.subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.add_circle_outline,
              color: const Color(0xFFFFCC00),
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(PaymentMethod method) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Payment Method',
              style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'Are you sure you want to delete ${method.name}? This action cannot be undone.',
              style: GoogleFonts.poppins(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel', style: GoogleFonts.poppins()),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _savedPaymentMethods.remove(method);
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Payment method deleted'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                child: Text(
                  'Delete',
                  style: GoogleFonts.poppins(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  void _setAsDefault(PaymentMethod method) {
    setState(() {
      for (var m in _savedPaymentMethods) {
        m.isDefault = false;
      }
      method.isDefault = true;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${method.name} set as default payment method'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _editPaymentMethod(PaymentMethod method) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => EditPaymentMethodScreen(
              paymentMethod: method,
              onSave: (updatedMethod) {
                setState(() {
                  final index = _savedPaymentMethods.indexOf(method);
                  if (index != -1) {
                    _savedPaymentMethods[index] = updatedMethod;
                  }
                });
              },
            ),
      ),
    );
  }

  void _addNewPaymentMethod(PaymentType type) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final String methodName;
    final String hintText;
    final IconData iconData;
    final List<TextInputType> keyboardTypes = [];
    final List<String> fieldLabels = [];
    final List<String> fieldHints = [];
    final List<TextEditingController> controllers = [];

    switch (type) {
      case PaymentType.upi:
        methodName = 'UPI';
        hintText = 'Enter your UPI ID';
        iconData = Icons.account_balance_wallet;

        // UPI fields
        fieldLabels.add('UPI ID');
        fieldHints.add('username@bank');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.text);

        fieldLabels.add('Name');
        fieldHints.add('Your full name');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.name);
        break;

      case PaymentType.creditCard:
      case PaymentType.debitCard:
        methodName =
            type == PaymentType.creditCard ? 'Credit Card' : 'Debit Card';
        hintText = 'Enter your card details';
        iconData = Icons.credit_card;

        // Card fields
        fieldLabels.add('Card Number');
        fieldHints.add('XXXX XXXX XXXX XXXX');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.number);

        fieldLabels.add('Cardholder Name');
        fieldHints.add('Name on card');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.name);

        fieldLabels.add('Expiry Date');
        fieldHints.add('MM/YY');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.datetime);

        fieldLabels.add('CVV');
        fieldHints.add('XXX');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.number);
        break;

      case PaymentType.bankAccount:
        methodName = 'Bank Account';
        hintText = 'Enter your bank account details';
        iconData = Icons.account_balance;

        // Bank account fields
        fieldLabels.add('Account Number');
        fieldHints.add('Your bank account number');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.number);

        fieldLabels.add('IFSC Code');
        fieldHints.add('Bank IFSC code');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.text);

        fieldLabels.add('Account Holder Name');
        fieldHints.add('Name as per bank records');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.name);

        fieldLabels.add('Bank Name');
        fieldHints.add('Your bank name');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.text);
        break;

      case PaymentType.paypal:
        methodName = 'PayPal';
        hintText = 'Link your PayPal account';
        iconData = Icons.paypal;

        // PayPal fields
        fieldLabels.add('PayPal Email');
        fieldHints.add('<EMAIL>');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.emailAddress);

        fieldLabels.add('Full Name');
        fieldHints.add('Your full name');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.name);
        break;

      case PaymentType.bitcoin:
        methodName = 'Bitcoin';
        hintText = 'Enter your Bitcoin wallet address';
        iconData = Icons.currency_bitcoin;

        // Bitcoin fields
        fieldLabels.add('Bitcoin Wallet Address');
        fieldHints.add('******************************************');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.text);

        fieldLabels.add('Wallet Label');
        fieldHints.add('Optional label for this wallet');
        controllers.add(TextEditingController());
        keyboardTypes.add(TextInputType.text);
        break;
    }

    // Form validation state
    bool formValid = false;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor:
                  isDarkMode ? const Color(0xFF1E1F26) : Colors.white,
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      iconData,
                      color: const Color(0xFFFFCC00),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      'Add $methodName',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              content: Container(
                width: double.maxFinite,
                constraints: const BoxConstraints(
                  maxWidth: 400,
                  maxHeight: 400,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hintText,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color:
                              isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 20),
                      for (int i = 0; i < fieldLabels.length; i++) ...[
                        Text(
                          fieldLabels[i],
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: controllers[i],
                          keyboardType: keyboardTypes[i],
                          obscureText: fieldLabels[i] == 'CVV',
                          style: GoogleFonts.poppins(
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                          decoration: InputDecoration(
                            hintText: fieldHints[i],
                            hintStyle: GoogleFonts.poppins(
                              color:
                                  isDarkMode
                                      ? Colors.grey[600]
                                      : Colors.grey[400],
                            ),
                            filled: true,
                            fillColor:
                                isDarkMode
                                    ? Colors.grey[800]
                                    : Colors.grey[100],
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide.none,
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 14,
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {
                              formValid = controllers.every(
                                (controller) =>
                                    controller.text.trim().isNotEmpty,
                              );
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                      ],
                      if (type == PaymentType.creditCard ||
                          type == PaymentType.debitCard) ...[
                        Row(
                          children: [
                            Checkbox(
                              value: false,
                              onChanged: (value) {},
                              activeColor: const Color(0xFFFFCC00),
                            ),
                            Expanded(
                              child: Text(
                                'Save card securely for future payments',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color:
                                      isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color:
                              isDarkMode
                                  ? Colors.grey[800]!.withOpacity(0.5)
                                  : Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                isDarkMode
                                    ? Colors.grey[700]!
                                    : Colors.grey[300]!,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.security,
                              size: 18,
                              color:
                                  isDarkMode
                                      ? Colors.grey[400]
                                      : Colors.grey[600],
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Your payment information is secured with industry-standard encryption',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color:
                                      isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed:
                      formValid
                          ? () {
                            Navigator.pop(context);

                            // Create appropriate payment method name based on user input
                            String paymentName = '';
                            switch (type) {
                              case PaymentType.upi:
                                paymentName = 'UPI - ${controllers[0].text}';
                                break;
                              case PaymentType.creditCard:
                              case PaymentType.debitCard:
                                // Mask card number, show only last 4 digits
                                String cardNumber = controllers[0].text;
                                String lastFour = '';
                                if (cardNumber.length >= 4) {
                                  lastFour = cardNumber.substring(
                                    cardNumber.length - 4,
                                  );
                                }
                                String cardType =
                                    type == PaymentType.creditCard
                                        ? 'Credit'
                                        : 'Debit';
                                paymentName = '$cardType Card ••••$lastFour';
                                break;
                              case PaymentType.bankAccount:
                                // Use bank name and mask account number
                                String accountNumber = controllers[0].text;
                                String lastFour = '';
                                if (accountNumber.length >= 4) {
                                  lastFour = accountNumber.substring(
                                    accountNumber.length - 4,
                                  );
                                }
                                paymentName =
                                    '${controllers[3].text} Account ••••$lastFour';
                                break;
                              case PaymentType.paypal:
                                paymentName = 'PayPal - ${controllers[0].text}';
                                break;
                              case PaymentType.bitcoin:
                                // Mask Bitcoin address, show first 6 and last 4 characters
                                String walletAddress = controllers[0].text;
                                String maskedAddress = '';
                                if (walletAddress.length > 10) {
                                  String first6 = walletAddress.substring(0, 6);
                                  String last4 = walletAddress.substring(
                                    walletAddress.length - 4,
                                  );
                                  maskedAddress = '$first6....$last4';
                                } else {
                                  maskedAddress = walletAddress;
                                }
                                String label =
                                    controllers[1].text.isNotEmpty
                                        ? controllers[1].text
                                        : 'Bitcoin Wallet';
                                paymentName = '$label - $maskedAddress';
                                break;
                            }

                            // Add the new payment method
                            final newMethod = PaymentMethod(
                              type: type,
                              name: paymentName,
                              isDefault: _savedPaymentMethods.isEmpty,
                              lastUsed: 'Added today',
                            );

                            setState(() {
                              _savedPaymentMethods.add(newMethod);
                            });

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('$methodName added successfully'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFCC00),
                    foregroundColor: Colors.black,
                    disabledBackgroundColor:
                        isDarkMode ? Colors.grey[800] : Colors.grey[300],
                    disabledForegroundColor:
                        isDarkMode ? Colors.grey[600] : Colors.grey[500],
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Save Payment Method',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

enum PaymentType { upi, creditCard, debitCard, bankAccount, paypal, bitcoin }

class PaymentMethod {
  final PaymentType type;
  final String name;
  bool isDefault;
  final String lastUsed;

  PaymentMethod({
    required this.type,
    required this.name,
    required this.isDefault,
    required this.lastUsed,
  });
}

class PaymentOption {
  final PaymentType type;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  PaymentOption({
    required this.type,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}
